"""
Citation Generator

This module generates citations for extracted data by finding where field names 
and values appear in the source documents using LLM analysis.
Supports multiple model providers: OpenAI, Anthropic, and AWS Bedrock.
"""

import json
import os
from pathlib import Path
from typing import Dict
from pydantic import BaseModel, Field
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude as AnthropicClaude
from agno.models.aws import Claude as AWSClaude
from agno.utils.log import logger
from textwrap import dedent
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Define OpenAI-compatible Pydantic model for citation structure
class CitationResult(BaseModel):
    # Use a more specific structure that OpenAI can handle
    citations: Dict[str, Dict[str, str]] = Field(..., description="Citations for each field")

    # Metadata fields (flattened)
    total_fields_analyzed: int = Field(..., description="Total number of fields analyzed")
    fields_with_field_citations: int = Field(..., description="Number of fields with field citations")
    fields_with_value_citations: int = Field(..., description="Number of fields with value citations")
    average_confidence: float = Field(..., description="Average confidence score")

def create_citation_model():
    """
    Create the appropriate model based on CITATION_MODEL_PROVIDER environment variable.
    
    Returns:
        Model instance for the specified provider
    """
    provider = os.getenv("CITATION_MODEL_PROVIDER", "openai").lower()
    
    if provider == "openai":
        logger.info("Using OpenAI model for citations")
        return OpenAIChat(id="gpt-4o")
    
    elif provider == "anthropic":
        logger.info("Using Anthropic direct API model for citations")
        return AnthropicClaude(id="claude-3-5-sonnet-20241022")
    
    elif provider == "bedrock":
        logger.info("Using AWS Bedrock Claude model for citations")
        try:
            bedrock_model_id = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0")
            aws_region = os.getenv("AWS_REGION", "us-east-1")
            model = AWSClaude(id=bedrock_model_id)
            logger.info(f"Bedrock model initialized: {bedrock_model_id} in region {aws_region}")
            return model
            
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock model: {e}")
            logger.info("Falling back to OpenAI model")
            return OpenAIChat(id="gpt-4o")
    
    else:
        logger.warning(f"Unknown provider '{provider}', falling back to OpenAI")
        return OpenAIChat(id="gpt-4o")

# Create the citation agent with the configured model
citation_agent = Agent(
    model=create_citation_model(),
    response_model=CitationResult,
    instructions=dedent("""\
You are a citation expert specializing in finding where extracted data fields and their values appear in source documents.

Your task is to analyze structured output from data extraction and find TWO types of citations for each field:

1. FIELD CITATION: Where does this field name/concept appear in the source?
   - Check extraction requirements for field_type definitions
   - Check markdown for field labels, headers, form fields
   - Look for: "Total:", "Supplier Name:", table headers, section labels, etc.

2. VALUE CITATION: Where does this exact value appear in the source?
   - Find exact matches in markdown text
   - Handle fuzzy matches for dates, numbers, currencies
   - Consider context and formatting variations
   - Look for values near field labels or in structured sections

ANALYSIS APPROACH:
- Use semantic understanding to match field concepts even with different wording
- Handle variations in formatting (dates, currencies, numbers)
- Assess confidence based on match quality and context
- Provide surrounding context for validation

CRITICAL: Your response must be ONLY valid JSON. No explanatory text, markdown formatting, or additional content.
"""),
    parser_model=create_citation_model(),
    markdown=False,
    show_tool_calls=False
)


def generate_citations(structured_output: dict, extraction_requirements: str, markdown_content: str, filename: str) -> CitationResult:
    """
    Generate citations using LLM analysis of structured output vs source documents.
    
    Args:
        structured_output: JSON result from extract_data_from_receipt()
        extraction_requirements: Compliance JSON string used for extraction  
        markdown_content: Markdown text used for extraction
        filename: For saving citation file
        
    Returns:
        Citation analysis results
    """
    try:
        provider = os.getenv("CITATION_MODEL_PROVIDER", "openai").lower()
        logger.info(f"Generating citations for {filename} using {provider} model")
        
        # Prepare the prompt with all three inputs
        prompt = f"""STRUCTURED OUTPUT (JSON):
{json.dumps(structured_output, indent=2)}

EXTRACTION REQUIREMENTS (JSON):
{extraction_requirements}

MARKDOWN TEXT:
{markdown_content}

Analyze the structured output and find field and value citations in the source documents."""

        # Get citation analysis from LLM
        response = citation_agent.run(prompt)

        # Handle RunResponse vs structured output
        if hasattr(response, 'content') and hasattr(response.content, '__dict__'):
            # If response.content is the structured model
            logger.debug("Extracting structured model from RunResponse.content")
            citations = response.content
        elif isinstance(response, CitationResult):
            # If response is already the structured model
            logger.debug("Response is already a CitationResult")
            citations = response
        else:
            # Log the issue and try to extract content
            logger.error(f"Unexpected response type: {type(response)}")
            if hasattr(response, 'content'):
                logger.error(f"Response content type: {type(response.content)}")
                logger.error(f"Response content: {response.content}")
            raise ValueError(f"Agent returned unexpected response type: {type(response)}")

        # Save citations to file (convert to dict for JSON serialization)
        save_citations(citations.model_dump(), filename)

        logger.info(f"Citations generated successfully for {filename} using {provider}")
        return citations
        
    except Exception as e:
        logger.error(f"Citation generation error for {filename}: {e}")
        return CitationResult(
            citations={},
            total_fields_analyzed=0,
            fields_with_field_citations=0,
            fields_with_value_citations=0,
            average_confidence=0.0
        )


def save_citations(citations: dict, filename: str):
    """
    Save citation results to file.
    
    Args:
        citations: Citation analysis results
        filename: Base filename (without extension)
    """
    try:
        # Create citation folder if it doesn't exist
        citation_folder = Path("citation_folder")
        citation_folder.mkdir(exist_ok=True)
        
        # Save citation file
        citation_file = citation_folder / f"{filename}_citation.json"
        with open(citation_file, 'w', encoding='utf-8') as f:
            json.dump(citations, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Citations saved to {citation_file}")
        
    except Exception as e:
        logger.error(f"Failed to save citations for {filename}: {e}")


def get_citation_stats(citations) -> dict:
    """
    Get statistics about citation quality.

    Args:
        citations: Citation analysis results (CitationResult object or dict)

    Returns:
        Citation statistics
    """
    # Handle both CitationResult objects and dict formats
    if isinstance(citations, CitationResult):
        citation_data = citations.citations
        if not citation_data:
            return {"error": "No citation data available"}
    elif isinstance(citations, dict):
        if not citations or "citations" not in citations:
            return {"error": "No citation data available"}
        citation_data = citations["citations"]
    else:
        return {"error": "Invalid citation data format"}
    total_fields = len(citation_data)

    field_citations = 0
    value_citations = 0
    total_confidence = 0.0
    confidence_count = 0

    for field_name, field_citations_data in citation_data.items():
        # Handle dict format (both new flattened and legacy formats)
        if isinstance(field_citations_data, dict):
            if "field_citation" in field_citations_data:
                field_citations += 1
                if "confidence" in field_citations_data["field_citation"]:
                    total_confidence += field_citations_data["field_citation"]["confidence"]
                    confidence_count += 1

            if "value_citation" in field_citations_data:
                value_citations += 1
                if "confidence" in field_citations_data["value_citation"]:
                    total_confidence += field_citations_data["value_citation"]["confidence"]
                    confidence_count += 1
    
    avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.0
    
    return {
        "total_fields": total_fields,
        "fields_with_field_citations": field_citations,
        "fields_with_value_citations": value_citations,
        "field_citation_rate": field_citations / total_fields if total_fields > 0 else 0.0,
        "value_citation_rate": value_citations / total_fields if total_fields > 0 else 0.0,
        "average_confidence": round(avg_confidence, 2)
    }
