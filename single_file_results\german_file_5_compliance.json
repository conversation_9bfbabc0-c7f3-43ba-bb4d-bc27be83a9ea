{"is_valid": false, "issues_count": 4, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "Country", "description": "The receipt country is Germany, but the requirements are specific to Switzerland.", "recommendation": "Ensure that the receipt pertains to the correct country of Switzerland for compliance with Global PPL CH GmbH.", "knowledge_base_reference": "File-related requirements - Must match the specified ICP and country."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Customer Name on Invoice", "description": "The receipt does not show Global PPL CH GmbH as the customer.", "recommendation": "Ensure the supplier includes Global PPL CH GmbH as the customer on the invoice.", "knowledge_base_reference": "Mandatory requirement for all receipts issued under Global PPL CH GmbH."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "<PERSON><PERSON><PERSON><PERSON>", "description": "The receipt is in EUR, while it should be in the local currency with FX rate calculation.", "recommendation": "Request a receipt in CHF or provide a detailed FX calculation from EUR to CHF.", "knowledge_base_reference": "File-related requirements for all expense types to be in local currency (CHF)."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Customer Address on Invoice", "description": "The receipt does not include the required customer address for Global PPL CH GmbH.", "recommendation": "Ensure the address \"Freigutstrasse 2 8002 Zürich, Switzerland\" is displayed on the supplier invoice.", "knowledge_base_reference": "Address must be provided for all invoices under Global PPL CH GmbH."}], "corrected_receipt": null, "compliance_summary": "The receipt has critical compliance issues related to the country, customer information, and currency. All issues need to be addressed to adhere to Switzerland-specific compliance for Global PPL CH GmbH.", "content_type": "ReceiptValidationResult", "country": "Switzerland", "icp": "Global People", "receipt_type": "All"}