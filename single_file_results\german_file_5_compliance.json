{"is_valid": false, "issues_count": 5, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "Customer Name on Invoice", "description": "The receipt is missing the mandatory field of the local employer name. The name \"Global PPL CH GmbH\" must be shown as the customer on the supplier invoice.", "recommendation": "It is recommended to address this issue with the supplier to include 'Global PPL CH GmbH' as the customer on the invoice.", "knowledge_base_reference": "Must show Global PPL CH GmbH as customer"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Customer Address on Invoice", "description": "The receipt is missing the mandatory local employer address. \"Freigutstrasse 2, 8002 Zürich, Switzerland\" must be the customer address on the supplier invoice.", "recommendation": "Please ensure that the address 'Freigutstrasse 2, 8002 Zürich, Switzerland' is included on the invoice.", "knowledge_base_reference": "Must show Freigutstrasse 2, 8002 Zurich, Switzerland"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Customer Registration on Invoice", "description": "The receipt omits the mandatory local employer registration \"CHE-295.369.918\" as the customer on the supplier invoice.", "recommendation": "Please have the supplier include the registration 'CHE-295.369.918' on the invoice.", "knowledge_base_reference": "Must show CHE-295.369.918"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "<PERSON><PERSON><PERSON><PERSON>", "description": "The currency on the receipt is EUR, but it should be the local currency (CHF) or include an FX rate calculation.", "recommendation": "Ensure that the receipt is issued in CHF or include a clear Foreign Exchange rate calculation.", "knowledge_base_reference": "Local currency with FX rate calculation"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Receipt Type", "description": "The document provided is a \"Quittung\", but the type of supporting document must be an actual tax receipt or invoice.", "recommendation": "Ensure that the document is a valid tax receipt or invoice rather than just a confirmation like 'Quittung'.", "knowledge_base_reference": "Must be actual tax receipts or invoices, not booking confirmations"}], "corrected_receipt": null, "compliance_summary": "The compliance analysis flagged several mandatory compliance violations related to the lack of required customer details and incorrect currency usage. Immediate actions should be taken to rectify these issues, specifically obtaining the complete invoice with mandatory information and in the correct format.", "content_type": "ReceiptValidationResult", "country": "Switzerland", "icp": "Global People", "receipt_type": "All"}