from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from agno.models.aws import <PERSON> as A<PERSON>Claude
from agno.utils.log import logger
from textwrap import dedent
from dotenv import load_dotenv
import os
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, root_validator

# Load environment variables
load_dotenv()

# Define Pydantic model for data extraction result
class LineItem(BaseModel):
    description: Optional[str] = Field(None, description="Description of the item")
    quantity: Optional[float] = Field(None, description="Quantity of the item")
    unit_price: Optional[float] = Field(None, description="Unit price of the item")
    total_price: Optional[float] = Field(None, description="Total price for this line item")

class DataExtractionResult(BaseModel):
    # Common fields from extraction requirements
    country: Optional[str] = Field(None, description="Country where the expense occurred")
    supplier_name: Optional[str] = Field(None, description="Name of the supplier or vendor")
    supplier_address: Optional[str] = Field(None, description="Address of the supplier")
    vat_number: Optional[str] = Field(None, description="VAT identification number")
    currency: Optional[str] = Field(None, description="Currency code (e.g., EUR, USD)")
    total_amount: Optional[float] = Field(None, description="Total amount of the expense")
    date_of_issue: Optional[str] = Field(None, description="Date when the receipt was issued (YYYY-MM-DD)")
    
    # Additional common fields
    transaction_time: Optional[str] = Field(None, description="Time of the transaction")
    receipt_type: Optional[str] = Field(None, description="Type of receipt (e.g., invoice, receipt)")
    contact_phone: Optional[str] = Field(None, description="Contact phone number")
    contact_email: Optional[str] = Field(None, description="Contact email address")
    contact_website: Optional[str] = Field(None, description="Website of the supplier")
    tax_rate: Optional[float] = Field(None, description="Tax rate applied")
    vat: Optional[float] = Field(None, description="VAT amount")
    subtotal: Optional[float] = Field(None, description="Subtotal amount before tax")
    payment_method: Optional[str] = Field(None, description="Method of payment")
    transaction_reference: Optional[str] = Field(None, description="Reference number for the transaction")
    line_items: Optional[List[LineItem]] = Field(None, description="Individual line items on the receipt")
    
    # Allow additional fields not explicitly defined
    class Config:
        extra = "allow"

def create_data_extraction_model():
    """
    Create the appropriate model based on DATA_EXTRACTION_MODEL_PROVIDER environment variable.
    
    Returns:
        Model instance for the specified provider
    """
    # Check specific provider first, then global fallback
    provider = os.getenv("DATA_EXTRACTION_MODEL_PROVIDER") or os.getenv("AGENT_MODEL_PROVIDER", "anthropic").lower()
    
    if provider == "openai":
        logger.info("Using OpenAI model for data extraction")
        return OpenAIChat(id="gpt-4o")
    
    elif provider == "anthropic":
        logger.info("Using Anthropic direct API model for data extraction")
        return AnthropicClaude(id="claude-3-7-sonnet-20250219")
    
    elif provider == "bedrock":
        logger.info("Using AWS Bedrock Claude model for data extraction")
        try:
            bedrock_model_id = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0")
            aws_region = os.getenv("AWS_REGION", "us-east-1")
            model = AWSClaude(id=bedrock_model_id)
            logger.info(f"Bedrock model initialized for data extraction: {bedrock_model_id} in region {aws_region}")
            return model
            
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock model for data extraction: {e}")
            logger.info("Falling back to Anthropic direct API model for data extraction")
            return AnthropicClaude(id="claude-3-7-sonnet-20250219")
    
    else:
        logger.warning(f"Unknown provider '{provider}' for data extraction, falling back to Anthropic")
        return AnthropicClaude(id="claude-3-7-sonnet-20250219")

# Create the data extraction agent
data_extraction_agent = Agent(
    model=create_data_extraction_model(),
    response_model=DataExtractionResult,
    instructions=dedent("""\
Persona: You are a meticulous and highly accurate data extraction AI. You specialize in parsing unstructured text from expense documents and structuring it into a precise JSON format. Your primary function is to identify and extract data points from the receipt text, not to validate them against specific rules.

Task: Your goal is to extract specific fields from the provided RECEIPT TEXT. You must use the EXTRACTION REQUIREMENTS as the definitive guide for what field types to look for. Additionally, identify and extract any other standard invoice or receipt fields not explicitly listed, including line items with their individual details.

INPUTS:
1. EXTRACTION REQUIREMENTS (JSON):
This JSON object defines the field types you must attempt to extract. Look at the "FieldType" values to understand what kinds of information to extract. The "Description" provides context to help you locate the correct information. IGNORE the "Rule" field completely - it is for validation purposes only and should not affect your extraction.

{extraction_requirements_json}

2. RECEIPT TEXT (MARKDOWN):
This is the raw text from the document that needs to be analyzed.

{receipt_text}

INSTRUCTIONS:
Analyze: Carefully read the RECEIPT TEXT to identify all available information.
Extract Field Types: For each unique "FieldType" in the EXTRACTION REQUIREMENTS, extract the actual value found in the receipt text. IGNORE any "Rule" specifications - extract what is actually present in the receipt.
Extract Additional Fields: Also extract any other relevant information found in the receipt that could be useful for expense management, such as:
- Line items with their details (products/services, quantities, prices)
- Transaction identifiers, reference numbers, or invoice numbers
- Date and time information
- Contact information (phone, email, website, etc.)
- Tax-related information (rates, amounts, tax IDs)
- Payment-related information
- Location or table identifiers
- Any special notes, terms, or conditions
- Subtotals, discounts, tips, or other financial breakdowns
- Any other structured data present in the receipt

Format: Structure your findings into a single, valid JSON object.
The keys of your output JSON MUST be the snake_case version of the "FieldType" values. For example, "Supplier Name" becomes "supplier_name", "VAT Number" becomes "vat_number".
For additional fields not in the extraction requirements, use descriptive snake_case field names that clearly indicate what the data represents.
If a field type cannot be found in the text, its value in the output JSON MUST be null. Do not guess or invent data.
For dates, standardize the format to YYYY-MM-DD. If you cannot determine the year, assume the current year.
For amounts and rates, extract only the numerical value (e.g., 120.50, 19.0).
For currency, use the standard 3-letter ISO code (e.g., "EUR", "USD") if possible; otherwise, extract the symbol.
For line items, create an array of objects with details like item name, quantity, unit price, and total price.
Adapt field names to the type of receipt (restaurant, hotel, transport, retail, etc.) while maintaining consistency.

CRITICAL REQUIREMENT:
Your final output MUST BE ONLY a valid JSON object. Do not include any explanatory text, greetings, apologies, or markdown formatting like ```json before or after the JSON object.
EXAMPLE OUTPUT STRUCTURE:
Include all fields from the extraction requirements (using snake_case of FieldType values) plus any additional relevant fields found in the receipt. Use descriptive field names for additional fields.

{
  "country": "Germany",
  "supplier_name": "THE SUSHI CLUB",
  "supplier_address": "Mohrenstr.42, 10117 Berlin",
  "vat_number": null,
  "currency": "EUR",
  "total_amount": 64.40,
  "date_of_issue": "2019-02-05",
  "line_items": [
    {
      "description": "Miso Soup",
      "quantity": 1,
      "unit_price": 3.90,
      "total_price": 3.90
    }
  ],
  "contact_phone": "+49 30 23 916 036",
  "contact_email": "<EMAIL>",
  "contact_website": "WWW.TheSushiClub.de",
  "transaction_time": "23:10:54",
  "receipt_type": "Rechnung",
  "table_number": "24",
  "transaction_reference": "L0001 FRÜH",
  "special_notes": "TIP IS NOT INCLUDED",
  "tax_rate": null,
  "vat": null,
  "name": null,
  "address": null,
  "supplier": null
}"""),
    #reasoning=True,
    # Ensure clean JSON output
    parser_model=create_data_extraction_model(),
    markdown=False,
    show_tool_calls=False
)

# Example usage function
def extract_data_from_receipt(extraction_requirement_json: str, receipt_text: str) -> str:
    """
    Extract data from receipt text using the specified extraction field types and additional relevant fields.

    Args:
        extraction_requirement_json: JSON string defining the field types to extract
        receipt_text: The raw receipt text to analyze

    Returns:
        JSON string with extracted data including specified fields and line items
    """
    # Format the prompt with the actual data
    formatted_prompt = f"""EXTRACTION REQUIREMENTS (JSON):
{extraction_requirement_json}

RECEIPT TEXT (MARKDOWN):
{receipt_text}"""
    
    # Get the response from the agent
    response = data_extraction_agent.run(formatted_prompt)
    
    # Return the response (will be a DataExtractionResult object)
    return response
