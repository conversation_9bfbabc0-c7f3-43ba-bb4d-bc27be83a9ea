from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import <PERSON> as <PERSON><PERSON><PERSON><PERSON>lau<PERSON>
from agno.models.aws import <PERSON> as A<PERSON>Claude
from agno.utils.log import logger
from textwrap import dedent
from dotenv import load_dotenv
import json
import os
from typing import List, Optional
from pydantic import BaseModel, Field

# Load environment variables
load_dotenv()

# Load expense file schema
def load_expense_schema():
    """Load the expense file schema for field-based classification."""
    schema_path = "expense_file_schema.json"
    if os.path.exists(schema_path):
        with open(schema_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        raise FileNotFoundError(f"Expense schema file not found: {schema_path}")

EXPENSE_SCHEMA = load_expense_schema()

# Define Pydantic model for schema field analysis
class SchemaFieldAnalysis(BaseModel):
    fields_found: List[str] = Field(..., description="List of schema fields identified in document")
    fields_missing: List[str] = Field(..., description="List of schema fields not found in document")
    total_fields_found: int = Field(..., description="Number of schema fields found")
    expense_identification_reasoning: str = Field(..., description="Detailed explanation citing exact fields found/missing for expense determination")

# Define Pydantic model for file classification result
class FileClassificationResult(BaseModel):
    is_expense: bool = Field(..., description="Whether this is an expense document")
    expense_type: Optional[str] = Field(None, description="Type of expense if is_expense is true")
    language: str = Field(..., description="Primary language of the document")
    language_confidence: float = Field(..., description="Confidence score for language identification (0-100)")
    document_location: Optional[str] = Field(None, description="Detected country/location from the document")
    expected_location: str = Field(..., description="Expected country/location provided in the input")
    location_match: bool = Field(..., description="Whether document location matches expected location")
    error_type: Optional[str] = Field(None, description="Error category if any issues found")
    error_message: Optional[str] = Field(None, description="Detailed error description")
    classification_confidence: Optional[float] = Field(None, description="Confidence score for classification (0-100)")
    reasoning: Optional[str] = Field(None, description="Brief explanation of classification decision")
    schema_field_analysis: Optional[SchemaFieldAnalysis] = Field(None, description="Analysis of schema fields found in the document")

def create_file_classification_model():
    """
    Create the appropriate model based on FILE_CLASSIFICATION_MODEL_PROVIDER environment variable.
    
    Returns:
        Model instance for the specified provider
    """
    # Check specific provider first, then global fallback
    provider = os.getenv("FILE_CLASSIFICATION_MODEL_PROVIDER") or os.getenv("AGENT_MODEL_PROVIDER", "openai").lower()
    
    if provider == "openai":
        logger.info("Using OpenAI model for file classification")
        return OpenAIChat(id="gpt-4o")
    
    elif provider == "anthropic":
        logger.info("Using Anthropic direct API model for file classification")
        return AnthropicClaude(id="claude-3-7-sonnet-20250219")
    
    elif provider == "bedrock":
        logger.info("Using AWS Bedrock Claude model for file classification")
        try:
            bedrock_model_id = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0")
            aws_region = os.getenv("AWS_REGION", "us-east-1")
            model = AWSClaude(id=bedrock_model_id)
            logger.info(f"Bedrock model initialized for file classification: {bedrock_model_id} in region {aws_region}")
            return model
            
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock model for file classification: {e}")
            logger.info("Falling back to OpenAI model for file classification")
            return OpenAIChat(id="gpt-4o")
    
    else:
        logger.warning(f"Unknown provider '{provider}' for file classification, falling back to OpenAI")
        return OpenAIChat(id="gpt-4o")

# Create the file classification agent
file_classification_agent = Agent(
    model=create_file_classification_model(),
    response_model=FileClassificationResult,
    instructions=dedent("""\
Persona: You are an expert file classification AI specializing in expense document analysis. Your primary function is to determine if a file contains expense-related content and classify it appropriately.

Task: Analyze the provided text to determine:
1. Whether this is an expense document (Y/N)
2. If it's an expense, classify the expense type
3. Identify the document language and confidence level
4. Verify location consistency

CLASSIFICATION CRITERIA:

STEP 1: EXPENSE IDENTIFICATION (SCHEMA-BASED)
First determine: Is this file an expense? (Y/N)

Use the provided EXPENSE FILE SCHEMA to identify expense documents based on field presence.

Look for each schema field in the document. If you find 5 or more fields, it's an expense document.

REQUIRED FOR EXPENSE CLASSIFICATION:
- Evidence of payment completed (not just booking/reservation)
- Actual amounts charged/paid
- Payment confirmation or receipt of transaction

NOT EXPENSES (even if business-related):
- Booking confirmations without payment proof
- Reservation details without charges shown
- Quotes, estimates, or pending invoices
- Payment details on next page (incomplete documents)


EXPENSE TYPE CLUSTERS (classify only if is_expense = true):
- flights: airline tickets, boarding passes, flight bookings, airport services
- meals: restaurants, food delivery, catering, dining, coffee shops, bars
- accommodation: hotels, lodging, room bookings, Airbnb, hostels, resorts
- telecommunications: phone bills, internet services, mobile plans, data charges
- travel: transportation (taxi, rideshare, bus, train), car rental, fuel, parking, tolls
- training: courses, workshops, educational services, conferences, seminars, certifications
- mileage: vehicle expenses, fuel receipts, car maintenance, parking fees
- entertainment: events, shows, client entertainment, team activities, sports events
- office_supplies: stationery, equipment, software licenses, office furniture
- utilities: electricity, water, gas, heating, cooling services
- professional_services: consulting, legal, accounting, marketing, IT services
- medical: healthcare services, medical consultations, pharmacy purchases
- other: miscellaneous business expenses not fitting above categories

LANGUAGE IDENTIFICATION:
Identify the primary language of the document and provide a confidence score (0-100%).
Consider factors like:
- Vocabulary and word patterns
- Grammar structures
- Currency symbols and formats
- Address formats
- Common phrases and expressions
Minimum confidence threshold: 80%

LOCATION VERIFICATION:
Extract the country/location from the document (from addresses, phone codes, currency, etc.)
Compare with the expected location provided in the input.

ERROR CATEGORIES AND HANDLING:
1. "File cannot be processed"
   - When: Technical issues, corrupted text, unreadable content, empty files
   - Action: Set is_expense=false, error_type="File cannot be processed"

2. "File identified not as an expense"
   - When: Text identified but doesn't fit expense definitions per location
   - Action: Set is_expense=false, error_type="File identified not as an expense"

3. "File cannot be analysed"
   - When: Language confidence below 80% threshold
   - Action: Set is_expense=false, error_type="File cannot be analysed"

4. "File location is not same as project's location"
   - When: Document location ≠ expected location input
   - Action: Set error_type="File location is not same as project's location"
   - Note: This can still be an expense, just flag the location mismatch

PROCESSING WORKFLOW:
1. First check if content is readable and processable
2. Identify language and calculate confidence score
3. Determine if content represents an expense document
4. If expense, classify the expense type cluster
5. Extract document location information
6. Compare document location with expected location
7. Set appropriate error flags if any issues found

CRITICAL REQUIREMENTS:
- Be conservative in classification - when in doubt, mark as not an expense
- Follow the exact error categories specified
- Provide clear reasoning for your decision"""),
    #reasoning=True,
    parser_model=create_file_classification_model(),
    markdown=False,
    show_tool_calls=False
)

def classify_file(receipt_text: str, expected_country: str = None) -> FileClassificationResult:
    """
    Classify a file to determine if it's an expense document and categorize it using schema-based field analysis.

    Args:
        receipt_text: The raw text content to analyze
        expected_country: The expected country/location for validation (optional)

    Returns:
        FileClassificationResult object with classification results including schema field analysis
    """
    # Create schema field descriptions for the prompt
    schema_fields_description = ""
    for field_name, field_info in EXPENSE_SCHEMA.get("properties", {}).items():
        title = field_info.get("title", field_name)
        description = field_info.get("description", "")
        schema_fields_description += f"\n**{field_name}** ({title}):\n{description}\n"

    # Format the prompt with the actual data and schema
    formatted_prompt = f"""EXPENSE FILE SCHEMA FIELDS:
{schema_fields_description}

DOCUMENT TEXT TO ANALYZE:
{receipt_text}

EXPECTED LOCATION: {expected_country if expected_country else "Not specified"}

ANALYSIS INSTRUCTIONS:
1. Carefully examine the document text for each of the 8 schema fields listed above
2. For each field, determine if it is PRESENT or ABSENT in the document
3. Use the field descriptions and recognition patterns to guide your analysis
4. Count the total number of fields found
5. Apply the expense identification logic (3-4+ fields = expense)
6. Provide detailed reasoning citing the exact fields found/missing

Analyze the above text following the schema-based workflow and provide classification results in the specified JSON format."""

    # Get the response from the agent
    response = file_classification_agent.run(formatted_prompt)

    # Debug logging
    logger.debug(f"Classification response type: {type(response)}")

    # Handle RunResponse vs structured output
    if hasattr(response, 'content') and hasattr(response.content, '__dict__'):
        # If response.content is the structured model
        logger.debug("Extracting structured model from RunResponse.content")
        return response.content
    elif isinstance(response, FileClassificationResult):
        # If response is already the structured model
        logger.debug("Response is already a FileClassificationResult")
        return response
    else:
        # Log the issue and try to extract content
        logger.error(f"Unexpected response type: {type(response)}")
        if hasattr(response, 'content'):
            logger.error(f"Response content type: {type(response.content)}")
            logger.error(f"Response content: {response.content}")
        raise ValueError(f"Agent returned unexpected response type: {type(response)}")
