{"austriaExpenseReimbursementDatabaseTables": {"fileRelatedRequirements": [{"fieldType": "Customer Name on Invoice", "description": "Local Employer name as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People IT-Services GmbH", "mandatoryOptional": "Mandatory", "rule": "Must show Global People IT-Services GmbH as customer"}, {"fieldType": "Customer Address on Invoice", "description": "Local Employer address as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People IT-Services GmbH", "mandatoryOptional": "Mandatory", "rule": "Must show Kärntner Ring 12, A-1010 Vienna, Austria"}, {"fieldType": "Customer VAT Number on Invoice", "description": "Local Employer VAT number as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People IT-Services GmbH", "mandatoryOptional": "Mandatory", "rule": "Must show ATU77112189"}, {"fieldType": "Customer Name Exception", "description": "Exception for flights/bookings where Local Employer name not possible", "receiptType": "Travel", "icpSpecific": true, "icpName": "Global People IT-Services GmbH", "mandatoryOptional": "Optional", "rule": "Worker's name acceptable when Local Employer name not possible, not end client"}, {"fieldType": "<PERSON><PERSON><PERSON><PERSON>", "description": "Receipt currency and exchange rate", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Local currency with FX rate calculation"}, {"fieldType": "Amount", "description": "Expense amount", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be clearly stated on receipt"}, {"fieldType": "Receipt Type", "description": "Type of supporting document", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be actual tax receipts or invoices, not booking confirmations"}, {"fieldType": "Receipt Quality", "description": "Document quality standard", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Online copies sufficient, hard copy not required"}, {"fieldType": "Personal Information", "description": "Privacy requirement for receipts", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Any personal information not required for reimbursement must be removed"}, {"fieldType": "Business Trip Reporting", "description": "Separate reports requirement", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Submit separate report for each trip"}, {"fieldType": "Travel Template", "description": "Specific reporting template", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must use Travel Expense Report Template Austria EUR.xlsx"}, {"fieldType": "Manager <PERSON><PERSON><PERSON><PERSON>", "description": "Direct manager approval", "receiptType": "Training", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Required for training expenses"}, {"fieldType": "Route Map", "description": "Travel route documentation", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Map with relevant route (Google Maps sufficient)"}, {"fieldType": "Kilometer Record", "description": "Distance traveled documentation", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Record of kilometers traveled must be submitted"}, {"fieldType": "Car Details", "description": "Vehicle information", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Car details and destination required"}, {"fieldType": "Parking Documentation", "description": "Parking expense", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Parking tickets should be included within the mileage payment"}], "complianceAndPolicies": [{"type": "Business Expenses (Non-Travel)", "description": "General business expenses for job completion", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Usually tax exempt, grossed up if not tax free", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide sufficient proof like tax receipts or invoices"}, {"type": "Home Office Costs", "description": "Home office expenses including furniture", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Specific allowances", "grossUpRule": "Follow specific tax allowances and rules", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must follow specific tax allowances found at government website"}, {"type": "Training", "description": "Training expenses", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Tax exempt with manager approval", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must get approval from your direct manager"}, {"type": "Mileage", "description": "Private vehicles, motorbikes, bicycles for work", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "€0.42 per km, max €12,600/year", "grossUpRule": "Maximum €0.42 per km, capped at €12,600 annually", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt is not applicable - you must provide map with route (Google Maps sufficient), record of kilometers, car details, and use travel template"}, {"type": "Parking", "description": "Parking expenses during mileage", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Included in mileage", "grossUpRule": "Should be included within mileage payment, taxed if paid separately", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - parking tickets should be included within mileage payment"}, {"type": "Domestic Business Travel", "description": "Domestic business trips over 25km", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "€30 per day max", "grossUpRule": "Maximum €30 per day (€2.50/hour for 3-12 hours), capped at €26.40 mentioned in document", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must use travel report template and cannot mix per diem with actual expenses"}, {"type": "Domestic Travel Per Diem", "description": "Meal allowances for domestic travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "€30 per day", "grossUpRule": "Tax-free per diem covers meals, reduced by 50% if meals provided", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - per diem method covers meals"}, {"type": "Domestic Travel Lodging", "description": "Accommodation for domestic travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "€17 without receipt", "grossUpRule": "Paid separately with proper invoice/receipt or €17", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - provide proper invoice/receipt or accept €17 flat rate"}, {"type": "International Business Travel", "description": "International business trips", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Government set rates", "grossUpRule": "Per diem rates set by government for each destination", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must use travel report template and cannot mix per diem with actual expenses"}, {"type": "International Travel Per Diem", "description": "Meal allowances for international travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Government set rates", "grossUpRule": "Tax-free based on government limits per destination, can be increased with receipts", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - for amounts above government rate, you must submit receipts"}, {"type": "International Travel Meals", "description": "Meal reductions for international travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Two-thirds reduction", "grossUpRule": "If lunch and dinner provided, per diem reduced by two-thirds", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - automatic reduction applies"}, {"type": "International Travel Expenses", "description": "Non-per diem expenses for international travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Expenses outside per diem paid against receipts", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for expenses outside per diem"}, {"type": "Per <PERSON> Method", "description": "Travel expense method consistency", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Cannot mix methods", "grossUpRule": "Cannot mix per diem method with actual expenses", "additionalInfoRequired": true, "additionalInfoDescription": "Cannot mix per diem method with actual expenses"}]}}